# 新增人数统计功能说明

## 功能概述

在GetInviteUserStatistics存储过程中新增了`period_new_invite_count`字段，用于统计指定时间区间内新增的下级用户总数。同时在前端添加了相应的数据卡片展示功能。

## 技术实现

### 1. 后端修改

#### 存储过程更新
- **文件**: `backend/jcloud-admin/src/main/resources/sql/zhubo.sql`
- **新增字段**: `period_new_invite_count`
- **计算逻辑**: 
  ```sql
  (SELECT COUNT(*) FROM vim_user WHERE invite_user = p_invite_user_id AND create_time >= p_start_time AND create_time <= p_end_time) AS period_new_invite_count
  ```

#### Java实体类更新
- **文件**: `backend/jcloud-admin/src/main/java/com/jcloud/admin/dto/response/AnchorStatsResponse.java`
- **新增字段**: `private Integer periodNewInviteCount;`

#### MyBatis映射更新
- **文件**: `backend/jcloud-admin/src/main/resources/mapper/OperationsMapper.xml`
- **新增映射**: `<result column="period_new_invite_count" property="periodNewInviteCount" jdbcType="INTEGER"/>`

### 2. 前端修改

#### TypeScript类型定义
- **文件**: `frontend/src/pages/yunying/types/operations.ts`
- **新增字段**: `periodNewInviteCount: number`

#### 数据卡片集成
- **文件**: `frontend/src/pages/yunying/hooks/useAnchorStats.ts`
- **新增卡片配置**:
  ```typescript
  {
    title: '新增人数',
    value: stats.periodNewInviteCount.toString(),
    unit: '人',
    description: '时间区间内新邀请的用户',
    trend: stats.periodNewInviteCount > 0 ? 'up' : 'stable',
    trendValue: `+${stats.periodNewInviteCount}`
  }
  ```

#### 独立卡片组件
- **文件**: `frontend/src/pages/yunying/components/NewInviteCountCard.tsx`
- **功能**: 提供独立的新增人数展示卡片组件

## 使用方法

### 1. 在主播统计页面中自动显示

新增人数卡片已经集成到主播统计页面的业务统计区域中，会自动显示在现有卡片列表中。

### 2. 使用独立卡片组件

```tsx
import { NewInviteCountCard } from '@/pages/yunying/components/NewInviteCountCard'

// 在组件中使用
<NewInviteCountCard 
  stats={anchorStats} 
  loading={loading}
  className="custom-class"
/>
```

### 3. API调用示例

```typescript
// 获取主播统计数据
const stats = await OperationsService.getAnchorStats(
  anchorId,
  startTime,
  endTime
)

// 访问新增人数字段
const newInviteCount = stats.periodNewInviteCount
```

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `periodNewInviteCount` | `number` | 指定时间区间内新邀请的下级用户总数 |

## 显示效果

- **卡片标题**: "新增人数"
- **数值显示**: 格式化的数字 + "人" 单位
- **描述信息**: "时间区间内新邀请的用户"
- **趋势指示**: 如果有新增用户，显示绿色上升箭头和"+X 新增"标签
- **权限控制**: 继承现有的权限控制机制

## 注意事项

1. **数据一致性**: 新字段与现有的`periodNewUserCount`字段逻辑相同，都统计时间区间内的新增用户
2. **空值处理**: 使用COUNT函数确保返回0而不是null
3. **权限控制**: 新卡片继承现有的运营模块权限控制
4. **性能影响**: 新增字段不会显著影响存储过程性能，因为使用了相同的查询逻辑

## 测试验证

可以使用提供的测试SQL文件验证功能：
```sql
-- 文件: backend/jcloud-admin/src/main/resources/sql/test_invite_statistics.sql
CALL GetInviteUserStatistics(1, 1640995200, 1672531199);
```

## 版本信息

- **版本**: v1.0.0
- **更新日期**: 2025-08-05
- **兼容性**: 向后兼容，不影响现有功能
