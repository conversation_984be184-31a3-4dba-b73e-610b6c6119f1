/**
 * 运营模块主页面
 *
 * 提供主播运营数据的查询和监控功能
 * 统一重构版本 - 完整的权限控制实现
 */

import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  BarChart3,
  Users,
  TrendingUp,
  ArrowLeft,
  CreditCard,
  ShoppingCart,
  User,
  Phone,
  Calendar,
  DollarSign,
  Shield,
  Key,
  // RefreshCw
} from 'lucide-react'
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/components/ui'
import { PagePermissionWrapper } from '@/components/auth/PermissionWrapper'
import { PermissionButton } from '@/components/auth/PermissionButton'
import { AnchorList } from './components/AnchorList'
import { AnchorStats } from './components/AnchorStats'
import type { AnchorListResponse } from './types/operations'
import { StatsCards, type OperationsStats } from '@/components/operations/StatsCards'
import { OperationsService } from '@/services/operations'
import { formatTimestamp, formatCurrency } from '@/utils'

/**
 * 页面视图枚举
 */
enum PageView {
  /** 主播列表 */
  ANCHOR_LIST = 'anchor-list',
  /** 主播统计 */
  ANCHOR_STATS = 'anchor-stats',
  /** 用户管理 */
  USER_MANAGEMENT = 'user-management'
}

/**
 * 运营模块主页面
 */
export default function YunyingPage() {
  const navigate = useNavigate()
  const [currentView, setCurrentView] = useState<PageView>(PageView.ANCHOR_LIST)
  const [selectedAnchor, setSelectedAnchor] = useState<AnchorListResponse | null>(null)

  // 统计数据状态
  const [statsData, setStatsData] = useState<OperationsStats | null>(null)
  const [statsLoading, setStatsLoading] = useState(false)
  const [statsError, setStatsError] = useState<string | null>(null)

  /**
   * 获取运营统计数据
   */
  const fetchStatsData = async () => {
    setStatsLoading(true)
    setStatsError(null)

    try {
      const data = await OperationsService.getOperationsStats()
      setStatsData(data)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取统计数据失败'
      setStatsError(errorMessage)
      console.error('获取运营统计数据失败:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  /**
   * 页面初始化时获取统计数据
   */
  useEffect(() => {
    fetchStatsData()
  }, [])

  /**
   * 处理主播选择
   */
  const handleSelectAnchor = (anchor: AnchorListResponse) => {
    setSelectedAnchor(anchor)
    setCurrentView(PageView.ANCHOR_STATS)
  }

  /**
   * 处理查看主播详情
   */
  const handleViewAnchorDetails = (anchor: AnchorListResponse) => {
    setSelectedAnchor(anchor)
    setCurrentView(PageView.USER_MANAGEMENT)
  }

  /**
   * 返回主播列表
   */
  const handleBackToList = () => {
    setCurrentView(PageView.ANCHOR_LIST)
    setSelectedAnchor(null)
  }

  /**
   * 处理查看下级用户
   */
  const handleViewSubUsers = (anchor: AnchorListResponse) => {
    navigate(`/yunying/sub-users?anchorId=${anchor.id}&anchorName=${encodeURIComponent(anchor.nickname)}`)
  }

  /**
   * 处理查看充值明细（主播维度）
   */
  const handleViewRechargeDetails = (anchor: AnchorListResponse) => {
    navigate(`/yunying/recharge-details?anchorId=${anchor.id}&anchorName=${encodeURIComponent(anchor.nickname)}&type=anchor`)
  }

  /**
   * 处理查看消费明细（主播维度）
   */
  const handleViewConsumeDetails = (anchor: AnchorListResponse) => {
    navigate(`/yunying/consume-details?anchorId=${anchor.id}&anchorName=${encodeURIComponent(anchor.nickname)}&type=anchor`)
  }

  /**
   * 用户状态映射
   */
  const USER_STATE_MAP = {
    1: { label: '正常', variant: 'success' as const },
    2: { label: '禁用', variant: 'destructive' as const }
  }

  /**
   * 身份类型映射
   */
  const IDENTITY_MAP = {
    2: { label: '线上主播', variant: 'default' as const },
    3: { label: '线下主播', variant: 'secondary' as const }
  }

  /**
   * 认证状态映射
   */
  const AUTH_STATUS_MAP = {
    0: { label: '未实名', variant: 'secondary' as const },
    1: { label: '已实名', variant: 'success' as const }
  }

  /**
   * 渲染用户管理视图
   */
  const renderUserManagement = (anchor: AnchorListResponse) => {
    return (
      <div className="space-y-6">
        {/* 主播基本信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              主播信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start space-x-6">
              {/* 头像 */}
              <Avatar className="h-16 w-16">
                <AvatarImage src={anchor.userimage} alt={anchor.nickname} />
                <AvatarFallback className="text-lg">
                  {anchor.nickname.charAt(0)}
                </AvatarFallback>
              </Avatar>

              {/* 基本信息 */}
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">用户ID</label>
                  <p className="text-sm font-mono">{anchor.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">昵称</label>
                  <p className="text-sm">{anchor.nickname}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">用户名</label>
                  <p className="text-sm">{anchor.username}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">手机号</label>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">{anchor.phone}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">状态</label>
                  <div>
                    <Badge variant={USER_STATE_MAP[anchor.state as keyof typeof USER_STATE_MAP]?.variant}>
                      {USER_STATE_MAP[anchor.state as keyof typeof USER_STATE_MAP]?.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">身份类型</label>
                  <div>
                    <Badge variant={IDENTITY_MAP[anchor.identity as keyof typeof IDENTITY_MAP]?.variant}>
                      {IDENTITY_MAP[anchor.identity as keyof typeof IDENTITY_MAP]?.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">实名状态</label>
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 mr-1 text-muted-foreground" />
                    <Badge variant={AUTH_STATUS_MAP[anchor.isauth as keyof typeof AUTH_STATUS_MAP]?.variant}>
                      {AUTH_STATUS_MAP[anchor.isauth as keyof typeof AUTH_STATUS_MAP]?.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">注册时间</label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">{formatTimestamp(anchor.createTime)}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">最后登录</label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">
                      {anchor.lastLoginTime ? formatTimestamp(anchor.lastLoginTime) : '从未登录'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 资产信息卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">余额</p>
                  <p className="text-2xl font-bold">{formatCurrency(anchor.coin)}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">钥匙</p>
                  <p className="text-2xl font-bold">{anchor.key}</p>
                </div>
                <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Key className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">下级用户</p>
                  <p className="text-2xl font-bold">{anchor.subUserCount}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 操作区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              用户管理操作
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 下级用户列表按钮 - 需要用户查看权限 */}
              <PermissionButton
                permissions={['operations:users:list']}
                variant="outline"
                className="h-20 flex-col space-y-2"
                onClick={() => handleViewSubUsers(anchor)}
                title="查看下级用户列表"
              >
                <Users className="h-8 w-8 text-blue-600" />
                <div className="text-center">
                  <div className="font-medium">下级用户列表</div>
                  <div className="text-xs text-muted-foreground">
                    查看 {anchor.subUserCount} 个下级用户
                  </div>
                </div>
              </PermissionButton>

              {/* 充值明细按钮 - 需要充值查看权限 */}
              <PermissionButton
                permissions={['operations:users:recharge']}
                variant="outline"
                className="h-20 flex-col space-y-2"
                onClick={() => handleViewRechargeDetails(anchor)}
                title="查看充值明细"
              >
                <CreditCard className="h-8 w-8 text-green-600" />
                <div className="text-center">
                  <div className="font-medium">充值明细</div>
                  <div className="text-xs text-muted-foreground">
                    查看充值记录
                  </div>
                </div>
              </PermissionButton>

              {/* 消费明细按钮 - 需要消费查看权限 */}
              <PermissionButton
                permissions={['operations:users:consume']}
                variant="outline"
                className="h-20 flex-col space-y-2"
                onClick={() => handleViewConsumeDetails(anchor)}
                title="查看消费明细"
              >
                <ShoppingCart className="h-8 w-8 text-orange-600" />
                <div className="text-center">
                  <div className="font-medium">消费明细</div>
                  <div className="text-xs text-muted-foreground">
                    查看消费记录
                  </div>
                </div>
              </PermissionButton>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  /**
   * 渲染页面头部
   */
  const renderPageHeader = () => {
    const getTitle = () => {
      switch (currentView) {
        case PageView.ANCHOR_LIST:
          return '主播管理'
        case PageView.ANCHOR_STATS:
          return `${selectedAnchor?.nickname} - 统计数据`
        case PageView.USER_MANAGEMENT:
          return `${selectedAnchor?.nickname} - 用户管理`
        default:
          return '运营管理'
      }
    }

    const getDescription = () => {
      switch (currentView) {
        case PageView.ANCHOR_LIST:
          return '查看和管理所有主播信息，监控主播业务数据'
        case PageView.ANCHOR_STATS:
          return '查看主播的详细业务统计数据和首充转化情况'
        case PageView.USER_MANAGEMENT:
          return '管理主播的下级用户，查看用户消费和充值详情'
        default:
          return '运营数据管理系统'
      }
    }

    return (
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          {currentView !== PageView.ANCHOR_LIST && (
            <Button variant="outline" onClick={handleBackToList}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold">{getTitle()}</h1>
            <p className="text-muted-foreground">{getDescription()}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-sm text-muted-foreground">
            <BarChart3 className="h-4 w-4" />
            <span>运营数据</span>
          </div>
        </div>
      </div>
    )
  }

  /**
   * 渲染统计概览卡片
   */
  const renderOverviewCards = () => {
    if (currentView !== PageView.ANCHOR_LIST) return null

    return (
      <div className="mb-6">
        <StatsCards
          stats={statsData}
          loading={statsLoading}
          error={statsError}
        />
      </div>
    )
  }

  /**
   * 渲染主要内容
   */
  const renderMainContent = () => {
    switch (currentView) {
      case PageView.ANCHOR_LIST:
        return (
          <AnchorList
            onSelectAnchor={handleSelectAnchor}
            onViewDetails={handleViewAnchorDetails}
          />
        )

      case PageView.ANCHOR_STATS:
        return (
          <AnchorStats
            anchor={selectedAnchor}
            onBack={handleBackToList}
          />
        )

      case PageView.USER_MANAGEMENT:
        return selectedAnchor ? renderUserManagement(selectedAnchor) : null
      default:
        return (
          <div className="text-center py-8 text-muted-foreground">
            页面不存在
          </div>
        )
    }
  }

  return (
    <PagePermissionWrapper module="operations">
      <div className="container mx-auto p-6 space-y-6">
        {/* 页面头部 */}
        {renderPageHeader()}

        {/* 统计概览 */}
        {renderOverviewCards()}

        {/* 主要内容 */}
        <Card>
          <CardContent className="p-6">
            {renderMainContent()}
          </CardContent>
        </Card>
      </div>
    </PagePermissionWrapper>
  )
}